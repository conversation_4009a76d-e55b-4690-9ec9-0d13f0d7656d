'use client';

import { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import Layout from '../../components/Layout';
import { <PERSON><PERSON>, Card, CardHeader, CardContent, Input, Loading } from '../../components/ui';
import WardrobeItemCard from '../../components/wardrobe/WardrobeItemCard';
import AddItemModal from '../../components/wardrobe/AddItemModal';
import { WardrobeItem, DatabaseService } from '../../lib/supabase';

export default function WardrobePage() {
  const { t } = useTranslation();
  const [items, setItems] = useState<WardrobeItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);

  // Mock user ID - in real app this would come from auth
  const userId = 'mock-user-id';

  useEffect(() => {
    loadWardrobeItems();
  }, []);

  const loadWardrobeItems = async () => {
    setLoading(true);
    try {
      // For now, we'll use mock data since we don't have auth set up
      const mockItems: WardrobeItem[] = [
        {
          id: '1',
          user_id: userId,
          name: 'Blue Denim Jacket',
          description: 'Classic blue denim jacket, perfect for casual wear',
          brand: 'Levi\'s',
          color: 'blue',
          size: 'M',
          price: 89.99,
          category_id: '1',
          seasons: ['spring', 'autumn'],
          occasions: ['casual'],
          is_favorite: true,
          wear_count: 5,
          tags: ['denim', 'jacket', 'casual'],
          image_url: '/placeholder-jacket.jpg',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          category: {
            id: '1',
            name: 'Outerwear',
            icon: '🧥',
            created_at: new Date().toISOString()
          }
        },
        {
          id: '2',
          user_id: userId,
          name: 'White Cotton T-Shirt',
          description: 'Basic white cotton t-shirt',
          brand: 'Uniqlo',
          color: 'white',
          size: 'L',
          price: 19.99,
          category_id: '2',
          seasons: ['spring', 'summer'],
          occasions: ['casual', 'sport'],
          is_favorite: false,
          wear_count: 12,
          tags: ['cotton', 't-shirt', 'basic'],
          image_url: '/placeholder-tshirt.jpg',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          category: {
            id: '2',
            name: 'Tops',
            icon: '👕',
            created_at: new Date().toISOString()
          }
        }
      ];
      setItems(mockItems);
    } catch (error) {
      console.error('Error loading wardrobe items:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleAddItem = async (newItem: Partial<WardrobeItem>) => {
    // Mock adding item
    const mockNewItem: WardrobeItem = {
      id: Date.now().toString(),
      user_id: userId,
      name: newItem.name || '',
      description: newItem.description,
      brand: newItem.brand,
      color: newItem.color,
      size: newItem.size,
      price: newItem.price,
      category_id: newItem.category_id,
      seasons: newItem.seasons || [],
      occasions: newItem.occasions || [],
      is_favorite: false,
      wear_count: 0,
      tags: newItem.tags || [],
      image_url: newItem.image_url,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    };

    setItems(prev => [mockNewItem, ...prev]);
    setIsAddModalOpen(false);
  };

  const handleDeleteItem = async (itemId: string) => {
    setItems(prev => prev.filter(item => item.id !== itemId));
  };

  const handleToggleFavorite = async (itemId: string) => {
    setItems(prev => prev.map(item => 
      item.id === itemId 
        ? { ...item, is_favorite: !item.is_favorite }
        : item
    ));
  };

  const filteredItems = items.filter(item => {
    const matchesSearch = item.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         item.brand?.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         item.tags?.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()));
    
    const matchesCategory = selectedCategory === 'all' || item.category_id === selectedCategory;
    
    return matchesSearch && matchesCategory;
  });

  const categories = [
    { id: 'all', name: t('common.all'), icon: '📦' },
    { id: '1', name: t('wardrobe.categories.outerwear'), icon: '🧥' },
    { id: '2', name: t('wardrobe.categories.tops'), icon: '👕' },
    { id: '3', name: t('wardrobe.categories.bottoms'), icon: '👖' },
    { id: '4', name: t('wardrobe.categories.dresses'), icon: '👗' },
    { id: '5', name: t('wardrobe.categories.shoes'), icon: '👠' },
    { id: '6', name: t('wardrobe.categories.accessories'), icon: '👜' },
  ];

  return (
    <Layout>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">
              {t('wardrobe.title')}
            </h1>
            <p className="mt-2 text-gray-600">
              Organize and manage your clothing items
            </p>
          </div>
          <div className="mt-4 sm:mt-0">
            <Button onClick={() => setIsAddModalOpen(true)}>
              {t('wardrobe.addItem')}
            </Button>
          </div>
        </div>

        {/* Filters */}
        <Card className="mb-6">
          <CardContent>
            <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0 lg:space-x-4">
              {/* Search */}
              <div className="flex-1 max-w-md">
                <Input
                  placeholder={t('common.search')}
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  leftIcon={
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                    </svg>
                  }
                />
              </div>

              {/* Category Filter */}
              <div className="flex flex-wrap gap-2">
                {categories.map((category) => (
                  <button
                    key={category.id}
                    onClick={() => setSelectedCategory(category.id)}
                    className={`px-3 py-2 rounded-lg text-sm font-medium transition-colors ${
                      selectedCategory === category.id
                        ? 'bg-blue-100 text-blue-700 border border-blue-200'
                        : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                    }`}
                  >
                    <span className="mr-1">{category.icon}</span>
                    {category.name}
                  </button>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Items Grid */}
        {loading ? (
          <div className="flex justify-center py-12">
            <Loading size="lg" text={t('common.loading')} />
          </div>
        ) : filteredItems.length === 0 ? (
          <Card>
            <CardContent>
              <div className="text-center py-12">
                <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                </svg>
                <h3 className="mt-2 text-sm font-medium text-gray-900">No items found</h3>
                <p className="mt-1 text-sm text-gray-500">
                  {searchQuery || selectedCategory !== 'all' 
                    ? 'Try adjusting your search or filters'
                    : 'Get started by adding your first wardrobe item'
                  }
                </p>
                {!searchQuery && selectedCategory === 'all' && (
                  <div className="mt-6">
                    <Button onClick={() => setIsAddModalOpen(true)}>
                      {t('wardrobe.addItem')}
                    </Button>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        ) : (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {filteredItems.map((item) => (
              <WardrobeItemCard
                key={item.id}
                item={item}
                onDelete={handleDeleteItem}
                onToggleFavorite={handleToggleFavorite}
              />
            ))}
          </div>
        )}

        {/* Add Item Modal */}
        <AddItemModal
          isOpen={isAddModalOpen}
          onClose={() => setIsAddModalOpen(false)}
          onAdd={handleAddItem}
        />
      </div>
    </Layout>
  );
}
