import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!;

export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: true
  }
});

// Database types
export interface Profile {
  id: string;
  email: string;
  full_name?: string;
  avatar_url?: string;
  language_preference: string;
  timezone: string;
  created_at: string;
  updated_at: string;
}

export interface WardrobeCategory {
  id: string;
  name: string;
  description?: string;
  icon?: string;
  created_at: string;
}

export interface WardrobeItem {
  id: string;
  user_id: string;
  category_id?: string;
  name: string;
  description?: string;
  brand?: string;
  color?: string;
  size?: string;
  price?: number;
  purchase_date?: string;
  image_url?: string;
  image_urls?: string[];
  tags?: string[];
  seasons?: string[];
  occasions?: string[];
  is_favorite: boolean;
  wear_count: number;
  last_worn?: string;
  notes?: string;
  created_at: string;
  updated_at: string;
  category?: WardrobeCategory;
}

export interface FridgeCategory {
  id: string;
  name: string;
  description?: string;
  icon?: string;
  created_at: string;
}

export interface FridgeLocation {
  id: string;
  user_id: string;
  name: string;
  description?: string;
  temperature_range?: string;
  created_at: string;
}

export interface FridgeItem {
  id: string;
  user_id: string;
  category_id?: string;
  location_id?: string;
  name: string;
  description?: string;
  brand?: string;
  quantity: number;
  unit?: string;
  purchase_date?: string;
  expiry_date?: string;
  image_url?: string;
  image_urls?: string[];
  tags?: string[];
  nutritional_info?: any;
  is_favorite: boolean;
  notes?: string;
  created_at: string;
  updated_at: string;
  category?: FridgeCategory;
  location?: FridgeLocation;
}

export interface AIRecommendation {
  id: string;
  user_id: string;
  type: 'outfit' | 'recipe';
  title: string;
  description?: string;
  items: any;
  ai_reasoning?: string;
  confidence_score?: number;
  weather_context?: any;
  occasion_context?: string;
  is_liked?: boolean;
  is_used: boolean;
  created_at: string;
  updated_at: string;
}

export interface OutfitCombination {
  id: string;
  user_id: string;
  name?: string;
  wardrobe_item_ids: string[];
  occasion?: string;
  season?: string;
  weather_conditions?: any;
  rating?: number;
  worn_date?: string;
  image_url?: string;
  notes?: string;
  created_at: string;
  updated_at: string;
}

export interface RecipeCollection {
  id: string;
  user_id: string;
  name: string;
  description?: string;
  fridge_item_ids: string[];
  recipe_content?: string;
  cooking_time?: number;
  difficulty_level?: number;
  rating?: number;
  cooked_date?: string;
  image_url?: string;
  notes?: string;
  created_at: string;
  updated_at: string;
}

// Database service functions
export class DatabaseService {
  // Profile operations
  static async getProfile(userId: string): Promise<Profile | null> {
    const { data, error } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', userId)
      .single();

    if (error) {
      console.error('Error fetching profile:', error);
      return null;
    }

    return data;
  }

  static async updateProfile(userId: string, updates: Partial<Profile>): Promise<Profile | null> {
    const { data, error } = await supabase
      .from('profiles')
      .update(updates)
      .eq('id', userId)
      .select()
      .single();

    if (error) {
      console.error('Error updating profile:', error);
      return null;
    }

    return data;
  }

  // Wardrobe operations
  static async getWardrobeItems(userId: string): Promise<WardrobeItem[]> {
    const { data, error } = await supabase
      .from('wardrobe_items')
      .select(`
        *,
        category:wardrobe_categories(*)
      `)
      .eq('user_id', userId)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching wardrobe items:', error);
      return [];
    }

    return data || [];
  }

  static async createWardrobeItem(item: Omit<WardrobeItem, 'id' | 'created_at' | 'updated_at'>): Promise<WardrobeItem | null> {
    const { data, error } = await supabase
      .from('wardrobe_items')
      .insert(item)
      .select()
      .single();

    if (error) {
      console.error('Error creating wardrobe item:', error);
      return null;
    }

    return data;
  }

  static async updateWardrobeItem(id: string, updates: Partial<WardrobeItem>): Promise<WardrobeItem | null> {
    const { data, error } = await supabase
      .from('wardrobe_items')
      .update(updates)
      .eq('id', id)
      .select()
      .single();

    if (error) {
      console.error('Error updating wardrobe item:', error);
      return null;
    }

    return data;
  }

  static async deleteWardrobeItem(id: string): Promise<boolean> {
    const { error } = await supabase
      .from('wardrobe_items')
      .delete()
      .eq('id', id);

    if (error) {
      console.error('Error deleting wardrobe item:', error);
      return false;
    }

    return true;
  }

  // Fridge operations
  static async getFridgeItems(userId: string): Promise<FridgeItem[]> {
    const { data, error } = await supabase
      .from('fridge_items')
      .select(`
        *,
        category:fridge_categories(*),
        location:fridge_locations(*)
      `)
      .eq('user_id', userId)
      .order('expiry_date', { ascending: true });

    if (error) {
      console.error('Error fetching fridge items:', error);
      return [];
    }

    return data || [];
  }

  static async createFridgeItem(item: Omit<FridgeItem, 'id' | 'created_at' | 'updated_at'>): Promise<FridgeItem | null> {
    const { data, error } = await supabase
      .from('fridge_items')
      .insert(item)
      .select()
      .single();

    if (error) {
      console.error('Error creating fridge item:', error);
      return null;
    }

    return data;
  }

  static async updateFridgeItem(id: string, updates: Partial<FridgeItem>): Promise<FridgeItem | null> {
    const { data, error } = await supabase
      .from('fridge_items')
      .update(updates)
      .eq('id', id)
      .select()
      .single();

    if (error) {
      console.error('Error updating fridge item:', error);
      return null;
    }

    return data;
  }

  static async deleteFridgeItem(id: string): Promise<boolean> {
    const { error } = await supabase
      .from('fridge_items')
      .delete()
      .eq('id', id);

    if (error) {
      console.error('Error deleting fridge item:', error);
      return false;
    }

    return true;
  }

  // Categories
  static async getWardrobeCategories(): Promise<WardrobeCategory[]> {
    const { data, error } = await supabase
      .from('wardrobe_categories')
      .select('*')
      .order('name');

    if (error) {
      console.error('Error fetching wardrobe categories:', error);
      return [];
    }

    return data || [];
  }

  static async getFridgeCategories(): Promise<FridgeCategory[]> {
    const { data, error } = await supabase
      .from('fridge_categories')
      .select('*')
      .order('name');

    if (error) {
      console.error('Error fetching fridge categories:', error);
      return [];
    }

    return data || [];
  }

  // AI Recommendations
  static async getAIRecommendations(userId: string, type?: 'outfit' | 'recipe'): Promise<AIRecommendation[]> {
    let query = supabase
      .from('ai_recommendations')
      .select('*')
      .eq('user_id', userId);

    if (type) {
      query = query.eq('type', type);
    }

    const { data, error } = await query.order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching AI recommendations:', error);
      return [];
    }

    return data || [];
  }

  static async createAIRecommendation(recommendation: Omit<AIRecommendation, 'id' | 'created_at' | 'updated_at'>): Promise<AIRecommendation | null> {
    const { data, error } = await supabase
      .from('ai_recommendations')
      .insert(recommendation)
      .select()
      .single();

    if (error) {
      console.error('Error creating AI recommendation:', error);
      return null;
    }

    return data;
  }
}
