-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- Users table (extends Supabase auth.users)
CREATE TABLE public.profiles (
    id UUID REFERENCES auth.users(id) ON DELETE CASCADE PRIMARY KEY,
    email TEXT UNIQUE NOT NULL,
    full_name TEXT,
    avatar_url TEXT,
    language_preference TEXT DEFAULT 'en',
    timezone TEXT DEFAULT 'UTC',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Wardrobe categories
CREATE TABLE public.wardrobe_categories (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    name TEXT NOT NULL,
    description TEXT,
    icon TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Wardrobe items
CREATE TABLE public.wardrobe_items (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE NOT NULL,
    category_id UUID REFERENCES public.wardrobe_categories(id) ON DELETE SET NULL,
    name TEXT NOT NULL,
    description TEXT,
    brand TEXT,
    color TEXT,
    size TEXT,
    price DECIMAL(10,2),
    purchase_date DATE,
    image_url TEXT,
    image_urls TEXT[], -- Array for multiple images
    tags TEXT[],
    seasons TEXT[] CHECK (seasons <@ ARRAY['spring', 'summer', 'autumn', 'winter']),
    occasions TEXT[] CHECK (occasions <@ ARRAY['casual', 'formal', 'business', 'party', 'sport', 'vacation']),
    is_favorite BOOLEAN DEFAULT FALSE,
    wear_count INTEGER DEFAULT 0,
    last_worn DATE,
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Fridge categories
CREATE TABLE public.fridge_categories (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    name TEXT NOT NULL,
    description TEXT,
    icon TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Fridge storage locations
CREATE TABLE public.fridge_locations (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE NOT NULL,
    name TEXT NOT NULL,
    description TEXT,
    temperature_range TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Fridge items
CREATE TABLE public.fridge_items (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE NOT NULL,
    category_id UUID REFERENCES public.fridge_categories(id) ON DELETE SET NULL,
    location_id UUID REFERENCES public.fridge_locations(id) ON DELETE SET NULL,
    name TEXT NOT NULL,
    description TEXT,
    brand TEXT,
    quantity INTEGER DEFAULT 1,
    unit TEXT, -- e.g., 'pieces', 'kg', 'liters', 'bottles'
    purchase_date DATE,
    expiry_date DATE,
    image_url TEXT,
    image_urls TEXT[], -- Array for multiple images
    tags TEXT[],
    nutritional_info JSONB,
    is_favorite BOOLEAN DEFAULT FALSE,
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- AI recommendations
CREATE TABLE public.ai_recommendations (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE NOT NULL,
    type TEXT NOT NULL CHECK (type IN ('outfit', 'recipe')),
    title TEXT NOT NULL,
    description TEXT,
    items JSONB, -- Array of item IDs and details
    ai_reasoning TEXT,
    confidence_score DECIMAL(3,2), -- 0.00 to 1.00
    weather_context JSONB,
    occasion_context TEXT,
    is_liked BOOLEAN,
    is_used BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Outfit combinations (for tracking successful outfits)
CREATE TABLE public.outfit_combinations (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE NOT NULL,
    name TEXT,
    wardrobe_item_ids UUID[] NOT NULL,
    occasion TEXT,
    season TEXT,
    weather_conditions JSONB,
    rating INTEGER CHECK (rating >= 1 AND rating <= 5),
    worn_date DATE,
    image_url TEXT,
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Recipe collections (for tracking successful recipes)
CREATE TABLE public.recipe_collections (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE NOT NULL,
    name TEXT NOT NULL,
    description TEXT,
    fridge_item_ids UUID[] NOT NULL,
    recipe_content TEXT,
    cooking_time INTEGER, -- in minutes
    difficulty_level INTEGER CHECK (difficulty_level >= 1 AND difficulty_level <= 5),
    rating INTEGER CHECK (rating >= 1 AND rating <= 5),
    cooked_date DATE,
    image_url TEXT,
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Insert default categories
INSERT INTO public.wardrobe_categories (name, description, icon) VALUES
    ('Tops', 'Shirts, blouses, t-shirts, sweaters', '👕'),
    ('Bottoms', 'Pants, jeans, skirts, shorts', '👖'),
    ('Dresses', 'Dresses and jumpsuits', '👗'),
    ('Outerwear', 'Jackets, coats, blazers', '🧥'),
    ('Shoes', 'All types of footwear', '👠'),
    ('Accessories', 'Bags, jewelry, belts, hats', '👜');

INSERT INTO public.fridge_categories (name, description, icon) VALUES
    ('Fruits', 'Fresh and dried fruits', '🍎'),
    ('Vegetables', 'Fresh vegetables and herbs', '🥕'),
    ('Meat', 'Meat, poultry, and seafood', '🥩'),
    ('Dairy', 'Milk, cheese, yogurt, eggs', '🥛'),
    ('Beverages', 'Drinks and liquids', '🥤'),
    ('Condiments', 'Sauces, spices, and seasonings', '🧂'),
    ('Leftovers', 'Prepared food and leftovers', '🍽️'),
    ('Frozen', 'Frozen foods and ice cream', '🧊');

-- Create indexes for better performance
CREATE INDEX idx_wardrobe_items_user_id ON public.wardrobe_items(user_id);
CREATE INDEX idx_wardrobe_items_category_id ON public.wardrobe_items(category_id);
CREATE INDEX idx_wardrobe_items_seasons ON public.wardrobe_items USING GIN(seasons);
CREATE INDEX idx_wardrobe_items_occasions ON public.wardrobe_items USING GIN(occasions);
CREATE INDEX idx_wardrobe_items_tags ON public.wardrobe_items USING GIN(tags);

CREATE INDEX idx_fridge_items_user_id ON public.fridge_items(user_id);
CREATE INDEX idx_fridge_items_category_id ON public.fridge_items(category_id);
CREATE INDEX idx_fridge_items_location_id ON public.fridge_items(location_id);
CREATE INDEX idx_fridge_items_expiry_date ON public.fridge_items(expiry_date);
CREATE INDEX idx_fridge_items_tags ON public.fridge_items USING GIN(tags);

CREATE INDEX idx_ai_recommendations_user_id ON public.ai_recommendations(user_id);
CREATE INDEX idx_ai_recommendations_type ON public.ai_recommendations(type);
CREATE INDEX idx_ai_recommendations_created_at ON public.ai_recommendations(created_at);

-- Row Level Security (RLS) policies
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.wardrobe_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.fridge_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.fridge_locations ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.ai_recommendations ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.outfit_combinations ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.recipe_collections ENABLE ROW LEVEL SECURITY;

-- Policies for profiles
CREATE POLICY "Users can view own profile" ON public.profiles FOR SELECT USING (auth.uid() = id);
CREATE POLICY "Users can update own profile" ON public.profiles FOR UPDATE USING (auth.uid() = id);
CREATE POLICY "Users can insert own profile" ON public.profiles FOR INSERT WITH CHECK (auth.uid() = id);

-- Policies for wardrobe_items
CREATE POLICY "Users can view own wardrobe items" ON public.wardrobe_items FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can insert own wardrobe items" ON public.wardrobe_items FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update own wardrobe items" ON public.wardrobe_items FOR UPDATE USING (auth.uid() = user_id);
CREATE POLICY "Users can delete own wardrobe items" ON public.wardrobe_items FOR DELETE USING (auth.uid() = user_id);

-- Policies for fridge_items
CREATE POLICY "Users can view own fridge items" ON public.fridge_items FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can insert own fridge items" ON public.fridge_items FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update own fridge items" ON public.fridge_items FOR UPDATE USING (auth.uid() = user_id);
CREATE POLICY "Users can delete own fridge items" ON public.fridge_items FOR DELETE USING (auth.uid() = user_id);

-- Policies for fridge_locations
CREATE POLICY "Users can view own fridge locations" ON public.fridge_locations FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can insert own fridge locations" ON public.fridge_locations FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update own fridge locations" ON public.fridge_locations FOR UPDATE USING (auth.uid() = user_id);
CREATE POLICY "Users can delete own fridge locations" ON public.fridge_locations FOR DELETE USING (auth.uid() = user_id);

-- Policies for ai_recommendations
CREATE POLICY "Users can view own AI recommendations" ON public.ai_recommendations FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can insert own AI recommendations" ON public.ai_recommendations FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update own AI recommendations" ON public.ai_recommendations FOR UPDATE USING (auth.uid() = user_id);
CREATE POLICY "Users can delete own AI recommendations" ON public.ai_recommendations FOR DELETE USING (auth.uid() = user_id);

-- Policies for outfit_combinations
CREATE POLICY "Users can view own outfit combinations" ON public.outfit_combinations FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can insert own outfit combinations" ON public.outfit_combinations FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update own outfit combinations" ON public.outfit_combinations FOR UPDATE USING (auth.uid() = user_id);
CREATE POLICY "Users can delete own outfit combinations" ON public.outfit_combinations FOR DELETE USING (auth.uid() = user_id);

-- Policies for recipe_collections
CREATE POLICY "Users can view own recipe collections" ON public.recipe_collections FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can insert own recipe collections" ON public.recipe_collections FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update own recipe collections" ON public.recipe_collections FOR UPDATE USING (auth.uid() = user_id);
CREATE POLICY "Users can delete own recipe collections" ON public.recipe_collections FOR DELETE USING (auth.uid() = user_id);

-- Functions for updating timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Triggers for updating timestamps
CREATE TRIGGER update_profiles_updated_at BEFORE UPDATE ON public.profiles FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_wardrobe_items_updated_at BEFORE UPDATE ON public.wardrobe_items FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_fridge_items_updated_at BEFORE UPDATE ON public.fridge_items FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_ai_recommendations_updated_at BEFORE UPDATE ON public.ai_recommendations FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_outfit_combinations_updated_at BEFORE UPDATE ON public.outfit_combinations FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_recipe_collections_updated_at BEFORE UPDATE ON public.recipe_collections FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
