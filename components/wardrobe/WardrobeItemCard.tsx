'use client';

import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import Image from 'next/image';
import { Card, CardContent, Button, ConfirmationModal } from '../ui';
import { WardrobeItem } from '../../lib/supabase';

interface WardrobeItemCardProps {
  item: WardrobeItem;
  onDelete: (itemId: string) => void;
  onToggleFavorite: (itemId: string) => void;
  onEdit?: (item: WardrobeItem) => void;
}

export default function WardrobeItemCard({ 
  item, 
  onDelete, 
  onToggleFavorite, 
  onEdit 
}: WardrobeItemCardProps) {
  const { t } = useTranslation();
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);

  const handleDelete = async () => {
    setIsDeleting(true);
    try {
      await onDelete(item.id);
      setShowDeleteModal(false);
    } catch (error) {
      console.error('Error deleting item:', error);
    } finally {
      setIsDeleting(false);
    }
  };

  const formatPrice = (price?: number) => {
    if (!price) return '';
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(price);
  };

  const getSeasonEmoji = (season: string) => {
    const seasonEmojis: Record<string, string> = {
      spring: '🌸',
      summer: '☀️',
      autumn: '🍂',
      winter: '❄️',
    };
    return seasonEmojis[season] || '';
  };

  const getOccasionColor = (occasion: string) => {
    const colors: Record<string, string> = {
      casual: 'bg-blue-100 text-blue-800',
      formal: 'bg-purple-100 text-purple-800',
      business: 'bg-gray-100 text-gray-800',
      party: 'bg-pink-100 text-pink-800',
      sport: 'bg-green-100 text-green-800',
      vacation: 'bg-yellow-100 text-yellow-800',
    };
    return colors[occasion] || 'bg-gray-100 text-gray-800';
  };

  return (
    <>
      <Card className="group hover:shadow-lg transition-shadow duration-200">
        <CardContent padding="none">
          {/* Image */}
          <div className="relative aspect-square bg-gray-100 rounded-t-lg overflow-hidden">
            {item.image_url ? (
              <Image
                src={item.image_url}
                alt={item.name}
                fill
                className="object-cover group-hover:scale-105 transition-transform duration-200"
              />
            ) : (
              <div className="flex items-center justify-center h-full text-gray-400">
                <svg className="w-12 h-12" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                </svg>
              </div>
            )}
            
            {/* Favorite Button */}
            <button
              onClick={() => onToggleFavorite(item.id)}
              className="absolute top-2 right-2 p-1.5 rounded-full bg-white/80 hover:bg-white transition-colors"
            >
              <svg
                className={`w-4 h-4 ${
                  item.is_favorite ? 'text-red-500 fill-current' : 'text-gray-400'
                }`}
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"
                />
              </svg>
            </button>

            {/* Category Badge */}
            {item.category && (
              <div className="absolute top-2 left-2 px-2 py-1 bg-white/90 rounded-full text-xs font-medium">
                <span className="mr-1">{item.category.icon}</span>
                {item.category.name}
              </div>
            )}
          </div>

          {/* Content */}
          <div className="p-4">
            {/* Title and Brand */}
            <div className="mb-2">
              <h3 className="font-semibold text-gray-900 truncate">{item.name}</h3>
              {item.brand && (
                <p className="text-sm text-gray-500">{item.brand}</p>
              )}
            </div>

            {/* Details */}
            <div className="space-y-2 mb-3">
              {/* Color and Size */}
              <div className="flex items-center space-x-4 text-sm text-gray-600">
                {item.color && (
                  <div className="flex items-center">
                    <div
                      className="w-3 h-3 rounded-full border border-gray-300 mr-1"
                      style={{ backgroundColor: item.color.toLowerCase() }}
                    />
                    <span className="capitalize">{item.color}</span>
                  </div>
                )}
                {item.size && (
                  <span className="font-medium">{t('common.size')}: {item.size}</span>
                )}
              </div>

              {/* Price and Wear Count */}
              <div className="flex items-center justify-between text-sm">
                {item.price && (
                  <span className="font-semibold text-green-600">
                    {formatPrice(item.price)}
                  </span>
                )}
                <span className="text-gray-500">
                  Worn {item.wear_count} times
                </span>
              </div>
            </div>

            {/* Seasons */}
            {item.seasons && item.seasons.length > 0 && (
              <div className="flex items-center space-x-1 mb-2">
                {item.seasons.map((season) => (
                  <span key={season} className="text-lg" title={season}>
                    {getSeasonEmoji(season)}
                  </span>
                ))}
              </div>
            )}

            {/* Occasions */}
            {item.occasions && item.occasions.length > 0 && (
              <div className="flex flex-wrap gap-1 mb-3">
                {item.occasions.slice(0, 2).map((occasion) => (
                  <span
                    key={occasion}
                    className={`px-2 py-1 rounded-full text-xs font-medium ${getOccasionColor(occasion)}`}
                  >
                    {t(`wardrobe.occasions.${occasion}`)}
                  </span>
                ))}
                {item.occasions.length > 2 && (
                  <span className="px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-600">
                    +{item.occasions.length - 2}
                  </span>
                )}
              </div>
            )}

            {/* Actions */}
            <div className="flex space-x-2">
              {onEdit && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => onEdit(item)}
                  className="flex-1"
                >
                  {t('common.edit')}
                </Button>
              )}
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowDeleteModal(true)}
                className="text-red-600 hover:text-red-700 hover:border-red-300"
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                </svg>
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Delete Confirmation Modal */}
      <ConfirmationModal
        isOpen={showDeleteModal}
        onClose={() => setShowDeleteModal(false)}
        onConfirm={handleDelete}
        title={t('common.confirm')}
        message={`Are you sure you want to delete "${item.name}"? This action cannot be undone.`}
        confirmText={t('common.delete')}
        cancelText={t('common.cancel')}
        variant="danger"
        loading={isDeleting}
      />
    </>
  );
}
