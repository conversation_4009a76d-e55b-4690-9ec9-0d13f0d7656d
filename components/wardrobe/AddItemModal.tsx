'use client';

import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>oot<PERSON>, Button, Input, Card } from '../ui';
import { WardrobeItem } from '../../lib/supabase';

interface AddItemModalProps {
  isOpen: boolean;
  onClose: () => void;
  onAdd: (item: Partial<WardrobeItem>) => void;
  editItem?: WardrobeItem;
}

export default function AddItemModal({ 
  isOpen, 
  onClose, 
  onAdd, 
  editItem 
}: AddItemModalProps) {
  const { t } = useTranslation();
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState({
    name: editItem?.name || '',
    description: editItem?.description || '',
    brand: editItem?.brand || '',
    color: editItem?.color || '',
    size: editItem?.size || '',
    price: editItem?.price?.toString() || '',
    category_id: editItem?.category_id || '',
    seasons: editItem?.seasons || [],
    occasions: editItem?.occasions || [],
    tags: editItem?.tags?.join(', ') || '',
  });

  const categories = [
    { id: '1', name: t('wardrobe.categories.outerwear'), icon: '🧥' },
    { id: '2', name: t('wardrobe.categories.tops'), icon: '👕' },
    { id: '3', name: t('wardrobe.categories.bottoms'), icon: '👖' },
    { id: '4', name: t('wardrobe.categories.dresses'), icon: '👗' },
    { id: '5', name: t('wardrobe.categories.shoes'), icon: '👠' },
    { id: '6', name: t('wardrobe.categories.accessories'), icon: '👜' },
  ];

  const seasons = [
    { id: 'spring', name: t('wardrobe.seasons.spring'), emoji: '🌸' },
    { id: 'summer', name: t('wardrobe.seasons.summer'), emoji: '☀️' },
    { id: 'autumn', name: t('wardrobe.seasons.autumn'), emoji: '🍂' },
    { id: 'winter', name: t('wardrobe.seasons.winter'), emoji: '❄️' },
  ];

  const occasions = [
    { id: 'casual', name: t('wardrobe.occasions.casual') },
    { id: 'formal', name: t('wardrobe.occasions.formal') },
    { id: 'business', name: t('wardrobe.occasions.business') },
    { id: 'party', name: t('wardrobe.occasions.party') },
    { id: 'sport', name: t('wardrobe.occasions.sport') },
    { id: 'vacation', name: t('wardrobe.occasions.vacation') },
  ];

  const colors = [
    'black', 'white', 'red', 'blue', 'green', 'yellow', 
    'pink', 'purple', 'orange', 'brown', 'gray', 'navy'
  ];

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    try {
      const itemData: Partial<WardrobeItem> = {
        name: formData.name,
        description: formData.description || undefined,
        brand: formData.brand || undefined,
        color: formData.color || undefined,
        size: formData.size || undefined,
        price: formData.price ? parseFloat(formData.price) : undefined,
        category_id: formData.category_id || undefined,
        seasons: formData.seasons,
        occasions: formData.occasions,
        tags: formData.tags ? formData.tags.split(',').map(tag => tag.trim()).filter(Boolean) : [],
      };

      await onAdd(itemData);
      handleClose();
    } catch (error) {
      console.error('Error adding item:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    setFormData({
      name: '',
      description: '',
      brand: '',
      color: '',
      size: '',
      price: '',
      category_id: '',
      seasons: [],
      occasions: [],
      tags: '',
    });
    onClose();
  };

  const handleSeasonToggle = (seasonId: string) => {
    setFormData(prev => ({
      ...prev,
      seasons: prev.seasons.includes(seasonId)
        ? prev.seasons.filter(s => s !== seasonId)
        : [...prev.seasons, seasonId]
    }));
  };

  const handleOccasionToggle = (occasionId: string) => {
    setFormData(prev => ({
      ...prev,
      occasions: prev.occasions.includes(occasionId)
        ? prev.occasions.filter(o => o !== occasionId)
        : [...prev.occasions, occasionId]
    }));
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={handleClose}
      title={editItem ? t('common.edit') + ' Item' : t('wardrobe.addItem')}
      size="lg"
    >
      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Basic Information */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <Input
            label={t('common.name')}
            value={formData.name}
            onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
            required
          />
          <Input
            label={t('common.brand')}
            value={formData.brand}
            onChange={(e) => setFormData(prev => ({ ...prev, brand: e.target.value }))}
          />
        </div>

        <Input
          label={t('common.description')}
          value={formData.description}
          onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
        />

        {/* Category */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            {t('common.category')}
          </label>
          <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
            {categories.map((category) => (
              <button
                key={category.id}
                type="button"
                onClick={() => setFormData(prev => ({ ...prev, category_id: category.id }))}
                className={`p-3 rounded-lg border text-left transition-colors ${
                  formData.category_id === category.id
                    ? 'border-blue-500 bg-blue-50 text-blue-700'
                    : 'border-gray-200 hover:border-gray-300'
                }`}
              >
                <span className="text-lg mr-2">{category.icon}</span>
                <span className="text-sm font-medium">{category.name}</span>
              </button>
            ))}
          </div>
        </div>

        {/* Color and Size */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              {t('common.color')}
            </label>
            <div className="grid grid-cols-4 gap-2">
              {colors.map((color) => (
                <button
                  key={color}
                  type="button"
                  onClick={() => setFormData(prev => ({ ...prev, color }))}
                  className={`p-2 rounded-lg border text-center transition-colors ${
                    formData.color === color
                      ? 'border-blue-500 bg-blue-50'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                >
                  <div
                    className="w-4 h-4 rounded-full mx-auto mb-1 border border-gray-300"
                    style={{ backgroundColor: color.toLowerCase() }}
                  />
                  <span className="text-xs capitalize">{color}</span>
                </button>
              ))}
            </div>
          </div>

          <Input
            label={t('common.size')}
            value={formData.size}
            onChange={(e) => setFormData(prev => ({ ...prev, size: e.target.value }))}
            placeholder="S, M, L, XL, etc."
          />
        </div>

        <Input
          label={t('common.price')}
          type="number"
          step="0.01"
          value={formData.price}
          onChange={(e) => setFormData(prev => ({ ...prev, price: e.target.value }))}
          placeholder="0.00"
        />

        {/* Seasons */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Seasons
          </label>
          <div className="flex flex-wrap gap-2">
            {seasons.map((season) => (
              <button
                key={season.id}
                type="button"
                onClick={() => handleSeasonToggle(season.id)}
                className={`px-3 py-2 rounded-lg border text-sm font-medium transition-colors ${
                  formData.seasons.includes(season.id)
                    ? 'border-blue-500 bg-blue-50 text-blue-700'
                    : 'border-gray-200 hover:border-gray-300'
                }`}
              >
                <span className="mr-1">{season.emoji}</span>
                {season.name}
              </button>
            ))}
          </div>
        </div>

        {/* Occasions */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Occasions
          </label>
          <div className="flex flex-wrap gap-2">
            {occasions.map((occasion) => (
              <button
                key={occasion.id}
                type="button"
                onClick={() => handleOccasionToggle(occasion.id)}
                className={`px-3 py-2 rounded-lg border text-sm font-medium transition-colors ${
                  formData.occasions.includes(occasion.id)
                    ? 'border-blue-500 bg-blue-50 text-blue-700'
                    : 'border-gray-200 hover:border-gray-300'
                }`}
              >
                {occasion.name}
              </button>
            ))}
          </div>
        </div>

        <Input
          label="Tags (comma separated)"
          value={formData.tags}
          onChange={(e) => setFormData(prev => ({ ...prev, tags: e.target.value }))}
          placeholder="casual, summer, favorite"
        />

        <ModalFooter>
          <Button variant="outline" onClick={handleClose} disabled={loading}>
            {t('common.cancel')}
          </Button>
          <Button type="submit" loading={loading}>
            {editItem ? t('common.save') : t('common.add')}
          </Button>
        </ModalFooter>
      </form>
    </Modal>
  );
}
