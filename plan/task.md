任务：开发AI赋能的智能物品管理 PWA 应用 (BFF 架构最终版)
项目目标： 创建一个基于 BFF 架构、可安装的 PWA。前端通过 Cloudflare 边缘网络与 Next.js 后端 API 进行交互，后端负责集成 Supabase、Azure GPT 和 Google Gemini 服务，为用户提供安全、高效的智能物品管理体验。

核心架构 (Backend-for-Frontend)
所有的数据流都将通过您部署在 Cloudflare 上的 Next.js 后端进行中转和处理。
```
graph TD
    subgraph 用户设备 (Browser)
        PWA[PWA 前端 (React)]
    end

    PWA -- HTTPS请求 --> Next_API

    subgraph Cloudflare Edge Network
        Next_API[Next.js API 路由 (BFF)]
    end

    Next_API -- 安全调用/查询 --> Supabase[Supabase: 数据库 + 存储]
    Next_API -- 安全调用 --> Azure[Azure: OpenAI GPT 服务]
    Next_API -- 安全调用 --> Google[Google AI: Gemini 服务]

    Supabase -- 数据 --> Next_API
    Azure -- AI结果 --> Next_API
    Google -- AI结果 --> Next_API

    Next_API -- JSON响应 --> PWA
```
此架构的优势：

安全：数据库、Azure、Google 的 API 密钥和访问凭证永远不会暴露到前端浏览器，全部安全地存储在 Cloudflare 的环境变量中。

稳定：无论外部 API 如何变化，只要您的 BFF 接口不变，前端就无需修改。

可控：您可以在 BFF 层添加缓存、日志、请求校验和复杂的业务逻辑。

核心功能需求 & 多语言支持
(此部分的核心业务逻辑和支持的国家列表保持不变)

开发计划 (BFF 架构修订版)
[✅] 步骤1：技术研究和AI集成架构设计 -> 研究步骤

部署架构研究： 深入研究使用 @cloudflare/next-on-pages 将 Next.js 应用部署到 Cloudflare Pages & Workers 的最佳实践。

BFF 架构设计： 设计 Next.js API 路由的结构，定义前端与后端之间的数据交换格式 (DTOs)。

AI 服务集成方案：

研究 Azure OpenAI Service 的 SDK/REST API (@azure/openai)。

研究 Google AI (Gemini) 的 SDK/REST API (@google/generative-ai)。

设计统一的 AI 推荐服务层，方便在 GPT 和 Gemini 之间切换或组合使用。

数据库设计： 保持不变。

PWA 核心技术研究： 保持不变（Service Worker, Manifest 等）。

[✅] 步骤2：获取后端服务凭据 -> 系统步骤

配置 Supabase 数据库和存储服务。

获取 Azure OpenAI Service 的 API Endpoint 和 Key。

获取 Google AI (Gemini) 的 API Key。

将所有凭据安全地配置为 Cloudflare Pages 的环境变量（Secrets）。

[✅] 步骤3：AI赋能的 PWA 应用开发 -> 全栈开发步骤

后端 BFF 开发 (Next.js API Routes)：

数据库接口层： 开发用于增删改查衣服、食物等数据的 API 路由 (例如 /api/items, /api/refrigerators)，内部调用 Supabase。

AI 代理层： 开发用于调用 AI 服务的 API 路由 (例如 /api/ai/recommend-outfit, /api/ai/analyze-image)，内部根据逻辑调用 Azure GPT 或 Google Gemini。

安全与验证： 在 API 路由中添加必要的输入验证。

前端 PWA 开发 (React)：

使用 Vite + React 初始化项目，配置 PWA 和国际化。

所有数据交互都通过 fetch 调用自己开发的 Next.js API 路由，不再直接使用 Supabase 的客户端库与数据库通信。

开发衣服管理、冰箱管理等模块的 UI 和交互逻辑。

集成 Service Worker 实现离线缓存（可缓存 BFF API 的 GET 请求结果）。

集成与测试：

在本地和 Cloudflare 的预览环境中进行端到端测试。

进行多语言和性能优化。

最终交付物 (BFF 架构修订版)
完整的AI赋能 PWA 项目源代码。

一套完整的后端 API 路由 (BFF)，作为应用的核心业务逻辑层。

响应式双模块应用 Web 界面。

针对 Cloudflare Pages 的部署配置文件和说明。

自定义 React UI 组件库和使用文档。

23种语言国际化支持。

详细的安装、配置和使用指南。

API 集成文档（包括内部 BFF API 文档）。

技术栈 (BFF 架构最终版)
前端框架： React.js (v18+) + Vite

全栈/后端框架： Next.js (App Router 或 Pages Router)

部署平台： Cloudflare Pages & Workers

Next.js 适配器： @cloudflare/next-on-pages

AI 服务：

Azure OpenAI Service (GPT-4, etc.)

Google AI Platform (Gemini)

数据库与存储： Supabase

后端 API 架构： Next.js API Routes 作为 BFF

状态管理： Zustand

路由： React Router (v6)

UI 组件库： Chakra UI / Shadcn/ui / Material-UI (MUI) + 自定义UI库

国际化： react-i18next

PWA 核心： Vite PWA Plugin

本地存储： IndexedDB & localStorage

安全： API 密钥和凭证通过 Cloudflare Environment Variables 管理