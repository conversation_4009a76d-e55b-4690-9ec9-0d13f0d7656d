const fs = require('fs');
const path = require('path');

// Basic template for missing languages
const basicTemplate = {
  "app": {
    "name": "AI Smart Item Manager",
    "description": "AI-powered smart item management for wardrobe and fridge"
  },
  "navigation": {
    "home": "Home",
    "wardrobe": "Wardrobe",
    "fridge": "Fridge",
    "recommendations": "AI Recommendations",
    "settings": "Settings",
    "profile": "Profile"
  },
  "common": {
    "add": "Add",
    "edit": "Edit",
    "delete": "Delete",
    "save": "Save",
    "cancel": "Cancel",
    "loading": "Loading...",
    "search": "Search"
  }
};

// Languages that need basic files
const languages = [
  'ru', 'it', 'ar', 'hi', 'th', 'vi', 'id', 'ms', 
  'nl', 'sv', 'no', 'da', 'fi', 'pl', 'tr'
];

// Create directories and files
languages.forEach(lang => {
  const dirPath = path.join(__dirname, '..', 'locales', lang);
  const filePath = path.join(dirPath, 'common.json');
  
  // Create directory if it doesn't exist
  if (!fs.existsSync(dirPath)) {
    fs.mkdirSync(dirPath, { recursive: true });
  }
  
  // Create file if it doesn't exist
  if (!fs.existsSync(filePath)) {
    fs.writeFileSync(filePath, JSON.stringify(basicTemplate, null, 2));
    console.log(`Created ${lang}/common.json`);
  }
});

console.log('Locale files generation complete!');
